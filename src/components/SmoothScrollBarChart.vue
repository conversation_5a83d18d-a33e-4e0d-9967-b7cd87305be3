<template>
  <div class="smooth-scroll-chart" ref="chartContainer">
    <!-- 标题栏 -->
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="scroll-indicator">
        <div class="indicator-track">
          <div 
            class="indicator-thumb" 
            :style="{ transform: `translateX(${scrollProgress * (100 - thumbWidth)}%)`, width: thumbWidth + '%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 图表主体 -->
    <div 
      class="chart-viewport" 
      ref="viewport"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
      @wheel="handleWheel"
    >
      <div 
        class="chart-content" 
        ref="content"
        :style="{ 
          transform: `translateX(${translateX}px)`,
          width: contentWidth + 'px'
        }"
      >
        <div class="bars-container">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="bar-item"
            :style="{ width: barWidth + 'px', marginRight: barSpacing + 'px' }"
          >
            <!-- 数值标签 -->
            <div class="bar-value" :class="{ 'visible': visibleBars.has(index) }">
              {{ item.value }}
            </div>
            
            <!-- 柱子本体 -->
            <div
              class="bar-column"
              :class="{ 'visible': visibleBars.has(index) }"
              :style="getBarStyle(item, index)"
              :data-debug="`index:${index}, visible:${visibleBars.has(index)}, height:${(item.value / maxValue) * props.maxBarHeight}px`"
            >
              <div class="bar-fill" :style="{ backgroundColor: getBarColor(index) }"></div>
              <div class="bar-highlight"></div>
            </div>
            
            <!-- 标签 -->
            <div class="bar-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 滑动提示 -->
    <div class="scroll-hint" :class="{ 'hidden': !showHint }">
      <span>👆 左右滑动查看更多数据</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

/**
 * 柱状图数据项接口
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
interface BarDataItem {
  label: string
  value: number
  color?: string
}

/**
 * 组件属性接口
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
interface Props {
  title?: string
  data: BarDataItem[]
  barWidth?: number
  barSpacing?: number
  maxBarHeight?: number
  colors?: string[]
  showHint?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '数据统计',
  barWidth: 50,
  barSpacing: 16,
  maxBarHeight: 200,
  colors: () => [
    '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f59e0b', 
    '#10b981', '#06b6d4', '#84cc16', '#f97316', '#94a3b8'
  ],
  showHint: true
})

// 响应式引用
const chartContainer = ref<HTMLElement>()
const viewport = ref<HTMLElement>()
const content = ref<HTMLElement>()
const translateX = ref(0)
const visibleBars = ref(new Set<number>())
const showHint = ref(true)

// 拖拽状态
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartTranslateX = ref(0)
const velocityX = ref(0)
const lastMoveTime = ref(0)
const lastMoveX = ref(0)

// 动画相关
let animationId: number = 0
let momentumTimeoutId: number = 0

/**
 * 计算内容总宽度
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const contentWidth = computed(() => {
  return props.data.length * (props.barWidth + props.barSpacing)
})

/**
 * 计算最大可滚动距离
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const maxTranslateX = computed(() => {
  if (!viewport.value) return 0
  const viewportWidth = viewport.value.clientWidth
  return Math.min(0, viewportWidth - contentWidth.value)
})

/**
 * 计算滚动进度
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const scrollProgress = computed(() => {
  if (maxTranslateX.value === 0) return 0
  return Math.abs(translateX.value) / Math.abs(maxTranslateX.value)
})

/**
 * 计算滚动条拇指宽度
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const thumbWidth = computed(() => {
  if (!viewport.value) return 100
  const viewportWidth = viewport.value.clientWidth
  const ratio = Math.min(1, viewportWidth / contentWidth.value)
  return Math.max(15, ratio * 100) // 最小15%宽度
})

/**
 * 获取数据最大值
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const maxValue = computed(() => {
  return Math.max(...props.data.map(item => item.value))
})

/**
 * 获取柱子样式
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const getBarStyle = (item: BarDataItem, index: number) => {
  const height = (item.value / maxValue.value) * props.maxBarHeight
  return {
    height: `${height}px`,
    animationDelay: `${index * 80}ms`
  }
}

/**
 * 获取柱子颜色
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const getBarColor = (index: number): string => {
  return props.colors[index % props.colors.length]
}

/**
 * 触摸开始处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleTouchStart = (e: TouchEvent) => {
  e.preventDefault()
  showHint.value = false
  startDrag(e.touches[0].clientX)
}

/**
 * 触摸移动处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleTouchMove = (e: TouchEvent) => {
  e.preventDefault()
  if (!isDragging.value) return
  updateDrag(e.touches[0].clientX)
}

/**
 * 触摸结束处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleTouchEnd = (e: TouchEvent) => {
  e.preventDefault()
  endDrag()
}

/**
 * 鼠标按下处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleMouseDown = (e: MouseEvent) => {
  e.preventDefault()
  showHint.value = false
  startDrag(e.clientX)
}

/**
 * 鼠标移动处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return
  e.preventDefault()
  updateDrag(e.clientX)
}

/**
 * 鼠标释放处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleMouseUp = () => {
  endDrag()
}

/**
 * 鼠标滚轮处理
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  showHint.value = false
  
  const delta = e.deltaX || e.deltaY
  const newTranslateX = translateX.value - delta * 0.5
  
  translateX.value = Math.max(maxTranslateX.value, Math.min(0, newTranslateX))
  updateVisibleBars()
}

/**
 * 开始拖拽
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const startDrag = (clientX: number) => {
  isDragging.value = true
  dragStartX.value = clientX
  dragStartTranslateX.value = translateX.value
  velocityX.value = 0
  lastMoveTime.value = Date.now()
  lastMoveX.value = clientX
  
  // 停止之前的动画
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = 0
  }
  if (momentumTimeoutId) {
    clearTimeout(momentumTimeoutId)
    momentumTimeoutId = 0
  }
}

/**
 * 更新拖拽
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const updateDrag = (clientX: number) => {
  const deltaX = clientX - dragStartX.value
  const newTranslateX = dragStartTranslateX.value + deltaX
  
  // 计算速度
  const now = Date.now()
  const timeDelta = now - lastMoveTime.value
  if (timeDelta > 0) {
    velocityX.value = (clientX - lastMoveX.value) / timeDelta
  }
  lastMoveTime.value = now
  lastMoveX.value = clientX
  
  // 边界弹性效果
  if (newTranslateX > 0) {
    translateX.value = newTranslateX * 0.3
  } else if (newTranslateX < maxTranslateX.value) {
    const overScroll = newTranslateX - maxTranslateX.value
    translateX.value = maxTranslateX.value + overScroll * 0.3
  } else {
    translateX.value = newTranslateX
  }
  
  updateVisibleBars()
}

/**
 * 结束拖拽
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const endDrag = () => {
  if (!isDragging.value) return
  isDragging.value = false
  
  // 如果超出边界，回弹
  if (translateX.value > 0 || translateX.value < maxTranslateX.value) {
    animateToPosition(Math.max(maxTranslateX.value, Math.min(0, translateX.value)))
    return
  }
  
  // 惯性滚动
  const absVelocity = Math.abs(velocityX.value)
  if (absVelocity > 0.5) {
    startMomentumScroll()
  }
}

/**
 * 开始惯性滚动
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const startMomentumScroll = () => {
  const friction = 0.95
  const minVelocity = 0.1
  
  const animate = () => {
    velocityX.value *= friction
    
    if (Math.abs(velocityX.value) < minVelocity) {
      // 速度太小，停止动画，检查边界
      if (translateX.value > 0 || translateX.value < maxTranslateX.value) {
        animateToPosition(Math.max(maxTranslateX.value, Math.min(0, translateX.value)))
      }
      return
    }
    
    const newTranslateX = translateX.value + velocityX.value * 16
    
    // 碰到边界时减速并回弹
    if (newTranslateX > 0) {
      velocityX.value *= -0.4
      animateToPosition(0)
      return
    } else if (newTranslateX < maxTranslateX.value) {
      velocityX.value *= -0.4
      animateToPosition(maxTranslateX.value)
      return
    }
    
    translateX.value = newTranslateX
    updateVisibleBars()
    
    animationId = requestAnimationFrame(animate)
  }
  
  animate()
}

/**
 * 动画移动到指定位置
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const animateToPosition = (targetX: number) => {
  const startX = translateX.value
  const distance = targetX - startX
  const duration = 500
  let startTime = 0
  
  const animate = (currentTime: number) => {
    if (!startTime) startTime = currentTime
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用弹性缓动函数
    const easeOutBack = (t: number) => {
      const c1 = 1.70158
      const c3 = c1 + 1
      return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2)
    }
    
    translateX.value = startX + distance * easeOutBack(progress)
    updateVisibleBars()
    
    if (progress < 1) {
      animationId = requestAnimationFrame(animate)
    }
  }
  
  animationId = requestAnimationFrame(animate)
}

/**
 * 更新可见柱子
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
const updateVisibleBars = () => {
  if (!viewport.value || !content.value || !props.data.length) {
    console.log('updateVisibleBars: 缺少必要条件', {
      viewport: !!viewport.value,
      content: !!content.value,
      dataLength: props.data.length
    })
    return
  }

  const viewportWidth = viewport.value.clientWidth
  const scrollLeft = -translateX.value
  const scrollRight = scrollLeft + viewportWidth

  console.log('updateVisibleBars: 视窗信息', {
    viewportWidth,
    scrollLeft,
    scrollRight,
    dataLength: props.data.length
  })

  const newVisibleBars = new Set<number>()

  props.data.forEach((item, index) => {
    const barLeft = index * (props.barWidth + props.barSpacing)
    const barRight = barLeft + props.barWidth

    // 有交集就认为可见，提前预加载
    if (barRight >= scrollLeft - 100 && barLeft <= scrollRight + 100) {
      newVisibleBars.add(index)
      console.log(`柱子 ${index} (${item.label}) 将显示`, { barLeft, barRight })

      // 立即添加到可见集合，然后通过CSS动画控制显示效果
      if (!visibleBars.value.has(index)) {
        visibleBars.value.add(index)
        console.log(`柱子 ${index} 已添加到可见集合`)
      }
    }
  })

  console.log('可见柱子数量:', newVisibleBars.size)

  // 移除不可见的柱子（延迟移除，保持动画效果）
  visibleBars.value.forEach(index => {
    if (!newVisibleBars.has(index)) {
      setTimeout(() => {
        visibleBars.value.delete(index)
      }, 300)
    }
  })
}

/**
 * 初始化组件
 * <AUTHOR>
 * @date 2025-08-18 17:33:56
 */
onMounted(async () => {
  await nextTick()
  
  // 立即显示前几个柱子
  updateVisibleBars()
  
  // 3秒后隐藏提示
  setTimeout(() => {
    showHint.value = false
  }, 3000)
  
  // 处理窗口大小变化
  const handleResize = () => {
    updateVisibleBars()
    // 确保不超出边界
    translateX.value = Math.max(maxTranslateX.value, Math.min(0, translateX.value))
  }
  
  window.addEventListener('resize', handleResize)
  
  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
    if (momentumTimeoutId) {
      clearTimeout(momentumTimeoutId)
    }
  })
})

// 监听数据变化，重新初始化可见状态
watch(() => props.data, (newData) => {
  console.log('数据变化', newData)
  visibleBars.value.clear()
  translateX.value = 0
  if (newData && newData.length > 0) {
    nextTick(() => {
      updateVisibleBars()
    })
  }
}, { deep: true, immediate: true })
</script>

<style scoped>
.smooth-scroll-chart {
  width: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scroll-indicator {
  flex: 1;
  max-width: 120px;
  margin-left: 20px;
}

.indicator-track {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.indicator-thumb {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.chart-viewport {
  height: 280px;
  overflow: hidden;
  position: relative;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  border-radius: 12px;
}

.chart-viewport:active {
  cursor: grabbing;
}

.chart-content {
  height: 100%;
  position: relative;
  will-change: transform;
}

.bars-container {
  display: flex;
  align-items: flex-end;
  height: 100%;
  padding: 20px 0;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  height: 100%;
  justify-content: flex-end;
}

.bar-value {
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.bar-value.visible {
  opacity: 1;
  transform: translateY(0);
}

.bar-column {
  position: relative;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.bar-column.visible {
  transform: scaleY(1);
  animation: barSlideIn 0.6s ease-out forwards;
}

.bar-fill {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.bar-highlight {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.bar-column:hover .bar-highlight {
  opacity: 1;
}

.bar-label {
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.scroll-hint {
  position: absolute;
  top: 24px;
  right: 24px;
  background: rgba(99, 102, 241, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 10;
}

.scroll-hint.hidden {
  opacity: 0;
  transform: translateY(-10px);
}

.scroll-hint span {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .smooth-scroll-chart {
    padding: 16px;
  }
  
  .chart-title {
    font-size: 18px;
  }
  
  .chart-viewport {
    height: 240px;
  }
  
  .scroll-hint {
    top: 16px;
    right: 16px;
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .scroll-indicator {
    max-width: 80px;
  }
}

/* 动画关键帧 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为柱子添加入场动画 */
@keyframes barSlideIn {
  0% {
    transform: scaleY(0);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 优化性能 */
.chart-content {
  transform: translate3d(var(--translate-x, 0), 0, 0);
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .indicator-thumb {
    box-shadow: 0 0.5px 1.5px rgba(0, 0, 0, 0.2);
  }
  
  .bar-column {
    box-shadow: 
      0 2px 3px -1px rgba(0, 0, 0, 0.1),
      0 1px 2px -1px rgba(0, 0, 0, 0.06);
  }
}
</style>
